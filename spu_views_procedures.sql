-- ===== VUES ET PROCÉDURES STOCKÉES POUR LE SYSTÈME FORMATIONS INITIALES =====
-- Système de formations initiales avec architecture hiérarchique
-- Base de données: gsa

USE gsa;

-- Supprimer les vues existantes si elles existent
DROP VIEW IF EXISTS vue_dashboard_formations;
DROP VIEW IF EXISTS vue_stagiaires_complets;
DROP VIEW IF EXISTS vue_fonctions_competences;
DROP VIEW IF EXISTS vue_cours_complets;
DROP VIEW IF EXISTS vue_planning_hebdomadaire;
DROP VIEW IF EXISTS vue_progression_stagiaires;

-- ===== VUES UTILES =====

-- Vue pour le tableau de bord général par formation
CREATE OR REPLACE VIEW vue_dashboard_formations AS
SELECT
    f.id as formation_id,
    f.nom as formation_nom,
    f.code as formation_code,
    (SELECT COUNT(*) FROM formations_stagiaires s WHERE s.formation_id = f.id AND s.statut = 'en_cours') as nb_stagiaires_actifs,
    (SELECT COUNT(DISTINCT fn.id) FROM formations_specialites sp
     JOIN formations_fonctions fn ON sp.id = fn.specialite_id
     WHERE sp.formation_id = f.id) as nb_fonctions_total,
    (SELECT COUNT(DISTINCT c.id) FROM formations_specialites sp
     JOIN formations_fonctions fn ON sp.id = fn.specialite_id
     JOIN formations_competences c ON fn.id = c.fonction_id
     WHERE sp.formation_id = f.id) as nb_competences_total,
    (SELECT COUNT(*) FROM formations_sessions_cours sc
     JOIN formations_cours co ON sc.cours_id = co.id
     JOIN formations_modules m ON co.module_id = m.id
     JOIN formations_specialites sp ON m.specialite_id = sp.id
     WHERE sp.formation_id = f.id AND DATE(sc.date_debut) = CURDATE()) as nb_sessions_aujourd_hui
FROM formations_initiales f
WHERE f.statut = 'active';

-- Vue détaillée des stagiaires avec progression hiérarchique
CREATE OR REPLACE VIEW vue_stagiaires_progression AS
SELECT
    s.id,
    s.matricule,
    s.nom,
    s.prenom,
    s.email,
    f.nom as formation,
    f.code as formation_code,
    sp.nom as specialite,
    sp.code as specialite_code,
    s.date_entree,
    s.date_sortie_prevue,
    s.statut,
    ROUND(AVG(ps.pourcentage_acquis), 2) as progression_globale,
    COUNT(DISTINCT ps.competence_id) as nb_competences_suivies,
    COUNT(DISTINCT ps.fonction_id) as nb_fonctions_suivies,
    SUM(CASE WHEN ps.statut = 'acquis' THEN 1 ELSE 0 END) as nb_competences_acquises,
    SUM(CASE WHEN ps.statut = 'en_retard' THEN 1 ELSE 0 END) as nb_competences_retard
FROM formations_stagiaires s
LEFT JOIN formations_initiales f ON s.formation_id = f.id
LEFT JOIN formations_specialites sp ON s.specialite_id = sp.id
LEFT JOIN formations_progression_stagiaires ps ON s.id = ps.stagiaire_id
WHERE s.statut = 'en_cours'
GROUP BY s.id, s.matricule, s.nom, s.prenom, s.email, f.nom, f.code, sp.nom, sp.code, s.date_entree, s.date_sortie_prevue, s.statut;

-- Vue des modules avec hiérarchie complète
CREATE OR REPLACE VIEW vue_modules_hierarchie AS
SELECT
    m.id,
    m.nom as module_nom,
    m.description as module_description,
    m.duree_totale_heures,
    m.ordre_sequence,
    f.nom as formation_nom,
    f.code as formation_code,
    sp.nom as specialite_nom,
    sp.code as specialite_code,
    COUNT(c.id) as nb_cours,
    SUM(c.volume_horaire) as total_heures_cours,
    m.statut
FROM formations_modules m
JOIN formations_specialites sp ON m.specialite_id = sp.id
JOIN formations_initiales f ON sp.formation_id = f.id
LEFT JOIN formations_cours c ON m.id = c.module_id AND c.statut = 'active'
WHERE m.statut = 'active'
GROUP BY m.id, m.nom, m.description, m.duree_totale_heures, m.ordre_sequence, f.nom, f.code, sp.nom, sp.code, m.statut;

-- Vue des cours avec informations complètes et hiérarchie
CREATE OR REPLACE VIEW vue_cours_complets AS
SELECT
    c.id,
    c.titre,
    c.description,
    c.volume_horaire,
    c.type_cours,
    c.lieu,
    m.nom as module_nom,
    sp.nom as specialite_nom,
    sp.code as specialite_code,
    f.nom as formation_nom,
    f.code as formation_code,
    GROUP_CONCAT(DISTINCT CONCAT(fn.code, '.', comp.code, ' (', cc.pourcentage_contribution, '%)') SEPARATOR ', ') as competences_associees,
    COUNT(DISTINCT cc.competence_id) as nb_competences_liees,
    c.statut
FROM formations_cours c
JOIN formations_modules m ON c.module_id = m.id
JOIN formations_specialites sp ON m.specialite_id = sp.id
JOIN formations_initiales f ON sp.formation_id = f.id
LEFT JOIN formations_cours_competences cc ON c.id = cc.cours_id
LEFT JOIN formations_competences comp ON cc.competence_id = comp.id
LEFT JOIN formations_fonctions fn ON comp.fonction_id = fn.id
WHERE c.statut = 'active'
GROUP BY c.id, c.titre, c.description, c.volume_horaire, c.type_cours, c.lieu, m.nom, sp.nom, sp.code, f.nom, f.code, c.statut;

-- Vue du planning hebdomadaire détaillé
CREATE OR REPLACE VIEW vue_planning_hebdomadaire AS
SELECT
    ph.id as planning_id,
    ph.semaine_debut,
    ph.semaine_fin,
    sp.nom as specialite,
    ch.jour_semaine,
    ch.heure_debut,
    ch.heure_fin,
    c.titre as cours_titre,
    CONCAT(f.nom, ' ', f.prenom) as formateur,
    sc.lieu,
    sc.statut as session_statut,
    ch.type_creneau
FROM spu_planning_hebdomadaire ph
JOIN spu_specialites sp ON ph.specialite_id = sp.id
JOIN spu_creneaux_horaires ch ON ph.id = ch.planning_id
LEFT JOIN spu_sessions_cours sc ON ch.session_id = sc.id
LEFT JOIN spu_cours c ON sc.cours_id = c.id
LEFT JOIN spu_formateurs f ON sc.formateur_id = f.id
WHERE ph.statut = 'valide'
ORDER BY ph.semaine_debut, ch.jour_semaine, ch.heure_debut;

-- Vue des évaluations avec détails
CREATE OR REPLACE VIEW vue_evaluations_detaillees AS
SELECT
    e.id,
    CONCAT(s.nom, ' ', s.prenom) as stagiaire,
    s.matricule,
    comp.code as competence_code,
    comp.nom as competence_nom,
    c.titre as cours_titre,
    e.type_evaluation,
    e.note,
    e.note_max,
    e.pourcentage,
    e.statut,
    e.date_evaluation,
    CONCAT(f.nom, ' ', f.prenom) as evaluateur,
    e.commentaires
FROM spu_evaluations e
JOIN spu_stagiaires s ON e.stagiaire_id = s.id
JOIN spu_competences comp ON e.competence_id = comp.id
LEFT JOIN spu_cours c ON e.cours_id = c.id
LEFT JOIN spu_formateurs f ON e.evaluateur_id = f.id
ORDER BY e.date_evaluation DESC;

-- Vue des stagiaires en retard
CREATE OR REPLACE VIEW vue_stagiaires_retard AS
SELECT
    s.id,
    s.matricule,
    CONCAT(s.nom, ' ', s.prenom) as stagiaire,
    sp.nom as specialite,
    comp.code as competence_code,
    comp.nom as competence_nom,
    ps.pourcentage_acquis,
    ps.statut,
    DATEDIFF(CURDATE(), ps.date_fin_prevue) as jours_retard,
    ps.date_fin_prevue
FROM spu_stagiaires s
JOIN spu_progression_stagiaires ps ON s.id = ps.stagiaire_id
JOIN spu_competences comp ON ps.competence_id = comp.id
JOIN spu_specialites sp ON s.specialite_id = sp.id
WHERE s.statut = 'en_cours'
AND (ps.statut = 'en_retard' OR (ps.date_fin_prevue < CURDATE() AND ps.statut != 'acquis'))
ORDER BY jours_retard DESC;

-- ===== PROCÉDURES STOCKÉES =====

DELIMITER //

-- Procédure pour calculer la progression d'un stagiaire
CREATE PROCEDURE CalculerProgressionStagiaire(IN stagiaire_id INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE comp_id INT;
    DECLARE total_heures INT;
    DECLARE heures_effectuees INT;
    DECLARE pourcentage DECIMAL(5,2);

    DECLARE cur CURSOR FOR
        SELECT c.id, c.duree_acquisition_heures
        FROM spu_competences c
        JOIN spu_specialites sp ON c.specialite_id = sp.id
        JOIN spu_stagiaires s ON s.specialite_id = sp.id
        WHERE s.id = stagiaire_id;

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    OPEN cur;

    read_loop: LOOP
        FETCH cur INTO comp_id, total_heures;
        IF done THEN
            LEAVE read_loop;
        END IF;

        -- Calculer les heures effectuées pour cette compétence
        SELECT COALESCE(SUM(c.volume_horaire), 0) INTO heures_effectuees
        FROM spu_participations p
        JOIN spu_sessions_cours sc ON p.session_id = sc.id
        JOIN spu_cours c ON sc.cours_id = c.id
        JOIN spu_cours_competences cc ON c.id = cc.cours_id
        WHERE p.stagiaire_id = stagiaire_id
        AND cc.competence_id = comp_id
        AND p.statut_presence = 'present';

        -- Calculer le pourcentage
        SET pourcentage = LEAST(100, (heures_effectuees / total_heures) * 100);

        -- Mettre à jour ou insérer la progression
        INSERT INTO spu_progression_stagiaires
            (stagiaire_id, competence_id, pourcentage_acquis, heures_effectuees, heures_requises, statut)
        VALUES
            (stagiaire_id, comp_id, pourcentage, heures_effectuees, total_heures,
             CASE
                WHEN pourcentage >= 100 THEN 'acquis'
                WHEN pourcentage >= 50 THEN 'en_cours'
                WHEN pourcentage > 0 THEN 'en_cours'
                ELSE 'non_commence'
             END)
        ON DUPLICATE KEY UPDATE
            pourcentage_acquis = pourcentage,
            heures_effectuees = heures_effectuees,
            heures_requises = total_heures,
            statut = CASE
                WHEN pourcentage >= 100 THEN 'acquis'
                WHEN pourcentage >= 50 THEN 'en_cours'
                WHEN pourcentage > 0 THEN 'en_cours'
                ELSE 'non_commence'
             END,
            derniere_mise_a_jour = CURRENT_TIMESTAMP;
    END LOOP;

    CLOSE cur;
END//

-- Procédure pour générer un planning hebdomadaire automatique
CREATE PROCEDURE GenererPlanningHebdomadaire(
    IN p_semaine_debut DATE,
    IN p_specialite_id INT,
    IN p_created_by INT
)
BEGIN
    DECLARE planning_id INT;
    DECLARE semaine_fin DATE;

    SET semaine_fin = DATE_ADD(p_semaine_debut, INTERVAL 4 DAY);

    -- Créer l'entrée de planning
    INSERT INTO spu_planning_hebdomadaire (semaine_debut, semaine_fin, specialite_id, created_by)
    VALUES (p_semaine_debut, semaine_fin, p_specialite_id, p_created_by);

    SET planning_id = LAST_INSERT_ID();

    -- Générer les créneaux par défaut (8h-12h et 13h-17h, Lundi-Vendredi)
    INSERT INTO spu_creneaux_horaires (planning_id, jour_semaine, heure_debut, heure_fin, type_creneau)
    VALUES
    (planning_id, 'lundi', '08:00', '10:00', 'cours'),
    (planning_id, 'lundi', '10:00', '12:00', 'cours'),
    (planning_id, 'lundi', '12:00', '13:00', 'pause'),
    (planning_id, 'lundi', '13:00', '15:00', 'cours'),
    (planning_id, 'lundi', '15:00', '17:00', 'cours'),

    (planning_id, 'mardi', '08:00', '10:00', 'cours'),
    (planning_id, 'mardi', '10:00', '12:00', 'cours'),
    (planning_id, 'mardi', '12:00', '13:00', 'pause'),
    (planning_id, 'mardi', '13:00', '15:00', 'cours'),
    (planning_id, 'mardi', '15:00', '17:00', 'cours'),

    (planning_id, 'mercredi', '08:00', '10:00', 'cours'),
    (planning_id, 'mercredi', '10:00', '12:00', 'cours'),
    (planning_id, 'mercredi', '12:00', '13:00', 'pause'),
    (planning_id, 'mercredi', '13:00', '15:00', 'cours'),
    (planning_id, 'mercredi', '15:00', '17:00', 'cours'),

    (planning_id, 'jeudi', '08:00', '10:00', 'cours'),
    (planning_id, 'jeudi', '10:00', '12:00', 'cours'),
    (planning_id, 'jeudi', '12:00', '13:00', 'pause'),
    (planning_id, 'jeudi', '13:00', '15:00', 'cours'),
    (planning_id, 'jeudi', '15:00', '17:00', 'cours'),

    (planning_id, 'vendredi', '08:00', '10:00', 'cours'),
    (planning_id, 'vendredi', '10:00', '12:00', 'cours'),
    (planning_id, 'vendredi', '12:00', '13:00', 'pause'),
    (planning_id, 'vendredi', '13:00', '15:00', 'cours'),
    (planning_id, 'vendredi', '15:00', '17:00', 'cours');

    SELECT planning_id as nouveau_planning_id;
END//

-- Procédure pour obtenir les statistiques d'un formateur
CREATE PROCEDURE StatistiquesFormateur(IN formateur_id INT)
BEGIN
    SELECT
        f.nom,
        f.prenom,
        f.grade,
        COUNT(DISTINCT sc.id) as nb_sessions_donnees,
        COUNT(DISTINCT p.stagiaire_id) as nb_stagiaires_formes,
        SUM(c.volume_horaire) as total_heures_enseignees,
        ROUND(AVG(e.note), 2) as note_moyenne_evaluations
    FROM spu_formateurs f
    LEFT JOIN spu_sessions_cours sc ON f.id = sc.formateur_id
    LEFT JOIN spu_cours c ON sc.cours_id = c.id
    LEFT JOIN spu_participations p ON sc.id = p.session_id
    LEFT JOIN spu_evaluations e ON f.id = e.evaluateur_id
    WHERE f.id = formateur_id
    GROUP BY f.id, f.nom, f.prenom, f.grade;
END//

DELIMITER ;
