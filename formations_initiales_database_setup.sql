-- ===== SCRIPT DE CRÉATION DES TABLES POUR LE SYSTÈME FORMATIONS INITIALES =====
-- Système de gestion des formations initiales avec architecture hiérarchique
-- Base de données: gsa

USE gsa;

-- ===== 1. TABLE DES FORMATIONS =====
CREATE TABLE IF NOT EXISTS formations_initiales (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(20) NOT NULL UNIQUE,
    nom VARCHAR(150) NOT NULL,
    description TEXT,
    duree_formation_semaines INT DEFAULT 12,
    statut ENUM('active', 'inactive', 'archive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ===== 2. TABLE DES SPÉCIALITÉS =====
CREATE TABLE IF NOT EXISTS formations_specialites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    formation_id INT NOT NULL,
    code VARCHAR(20) NOT NULL,
    nom VARCHAR(150) NOT NULL,
    description TEXT,
    duree_specialite_semaines INT DEFAULT 8,
    statut ENUM('active', 'inactive', 'archive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (formation_id) REFERENCES formations_initiales(id) ON DELETE CASCADE,
    UNIQUE KEY unique_formation_specialite (formation_id, code)
);

-- ===== 3. TABLE DES FONCTIONS =====
CREATE TABLE IF NOT EXISTS formations_fonctions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    specialite_id INT NOT NULL,
    code VARCHAR(20) NOT NULL,
    nom VARCHAR(150) NOT NULL,
    description TEXT,
    duree_fonction_heures INT DEFAULT 40,
    niveau_requis ENUM('debutant', 'intermediaire', 'avance') DEFAULT 'debutant',
    statut ENUM('active', 'inactive', 'archive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (specialite_id) REFERENCES formations_specialites(id) ON DELETE CASCADE,
    UNIQUE KEY unique_specialite_fonction (specialite_id, code)
);

-- ===== 4. TABLE DES COMPÉTENCES =====
CREATE TABLE IF NOT EXISTS formations_competences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    fonction_id INT NOT NULL,
    code VARCHAR(20) NOT NULL,
    nom VARCHAR(150) NOT NULL,
    description TEXT,
    duree_acquisition_heures INT DEFAULT 10,
    niveau_requis ENUM('debutant', 'intermediaire', 'avance') DEFAULT 'debutant',
    coefficient DECIMAL(3,2) DEFAULT 1.00,
    statut ENUM('active', 'inactive', 'archive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (fonction_id) REFERENCES formations_fonctions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_fonction_competence (fonction_id, code)
);

-- ===== 5. TABLE DES MODULES =====
CREATE TABLE IF NOT EXISTS formations_modules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(150) NOT NULL,
    description TEXT,
    specialite_id INT NOT NULL,
    duree_totale_heures INT DEFAULT 0,
    ordre_sequence INT DEFAULT 1,
    statut ENUM('active', 'inactive', 'archive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (specialite_id) REFERENCES formations_specialites(id) ON DELETE CASCADE
);

-- ===== 6. TABLE DES COURS =====
CREATE TABLE IF NOT EXISTS formations_cours (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(150) NOT NULL,
    description TEXT,
    module_id INT NOT NULL,
    volume_horaire INT NOT NULL,
    type_cours ENUM('theorique', 'pratique', 'mixte', 'evaluation') DEFAULT 'theorique',
    lieu VARCHAR(100),
    materiel_requis TEXT,
    prerequis TEXT,
    objectifs TEXT,
    statut ENUM('active', 'inactive', 'archive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (module_id) REFERENCES formations_modules(id) ON DELETE CASCADE
);

-- ===== 7. TABLE DE LIAISON COURS-COMPÉTENCES =====
CREATE TABLE IF NOT EXISTS formations_cours_competences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cours_id INT NOT NULL,
    competence_id INT NOT NULL,
    pourcentage_contribution DECIMAL(5,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (cours_id) REFERENCES formations_cours(id) ON DELETE CASCADE,
    FOREIGN KEY (competence_id) REFERENCES formations_competences(id) ON DELETE CASCADE,
    UNIQUE KEY unique_cours_competence (cours_id, competence_id)
);

-- ===== 8. TABLE DES STAGIAIRES =====
CREATE TABLE IF NOT EXISTS formations_stagiaires (
    id INT AUTO_INCREMENT PRIMARY KEY,
    matricule VARCHAR(20) NOT NULL UNIQUE,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(150),
    telephone VARCHAR(20),
    formation_id INT NOT NULL,
    specialite_id INT,
    date_entree DATE NOT NULL,
    date_sortie_prevue DATE,
    date_sortie_reelle DATE,
    statut ENUM('en_cours', 'termine', 'abandonne', 'suspendu') DEFAULT 'en_cours',
    notes_generales TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (formation_id) REFERENCES formations_initiales(id) ON DELETE CASCADE,
    FOREIGN KEY (specialite_id) REFERENCES formations_specialites(id) ON DELETE SET NULL
);

-- ===== 9. TABLE DES FORMATEURS =====
CREATE TABLE IF NOT EXISTS formations_formateurs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    matricule VARCHAR(20) NOT NULL UNIQUE,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(150),
    telephone VARCHAR(20),
    grade VARCHAR(50),
    formations_autorisees TEXT, -- JSON array des formations autorisées
    specialites_autorisees TEXT, -- JSON array des spécialités autorisées
    statut ENUM('actif', 'inactif', 'conge') DEFAULT 'actif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ===== 10. TABLE DES SESSIONS DE COURS =====
CREATE TABLE IF NOT EXISTS formations_sessions_cours (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cours_id INT NOT NULL,
    formateur_id INT,
    date_debut DATETIME NOT NULL,
    date_fin DATETIME NOT NULL,
    lieu VARCHAR(100),
    capacite_max INT DEFAULT 20,
    statut ENUM('planifie', 'en_cours', 'termine', 'annule') DEFAULT 'planifie',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (cours_id) REFERENCES formations_cours(id) ON DELETE CASCADE,
    FOREIGN KEY (formateur_id) REFERENCES formations_formateurs(id) ON DELETE SET NULL
);

-- ===== 11. TABLE DES PARTICIPATIONS =====
CREATE TABLE IF NOT EXISTS formations_participations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id INT NOT NULL,
    stagiaire_id INT NOT NULL,
    statut_presence ENUM('present', 'absent', 'retard', 'excuse') DEFAULT 'present',
    date_participation DATETIME NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES formations_sessions_cours(id) ON DELETE CASCADE,
    FOREIGN KEY (stagiaire_id) REFERENCES formations_stagiaires(id) ON DELETE CASCADE,
    UNIQUE KEY unique_session_stagiaire (session_id, stagiaire_id)
);

-- ===== 12. TABLE DES ÉVALUATIONS =====
CREATE TABLE IF NOT EXISTS formations_evaluations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stagiaire_id INT NOT NULL,
    competence_id INT NOT NULL,
    cours_id INT,
    session_id INT,
    type_evaluation ENUM('formative', 'sommative', 'pratique', 'theorique', 'fonction') NOT NULL,
    note DECIMAL(4,2),
    note_max DECIMAL(4,2) DEFAULT 20.00,
    pourcentage DECIMAL(5,2),
    statut ENUM('en_attente', 'validee', 'rattrapage') DEFAULT 'en_attente',
    date_evaluation DATE NOT NULL,
    commentaires TEXT,
    evaluateur_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (stagiaire_id) REFERENCES formations_stagiaires(id) ON DELETE CASCADE,
    FOREIGN KEY (competence_id) REFERENCES formations_competences(id) ON DELETE CASCADE,
    FOREIGN KEY (cours_id) REFERENCES formations_cours(id) ON DELETE SET NULL,
    FOREIGN KEY (session_id) REFERENCES formations_sessions_cours(id) ON DELETE SET NULL,
    FOREIGN KEY (evaluateur_id) REFERENCES formations_formateurs(id) ON DELETE SET NULL
);

-- ===== 13. TABLE DE PROGRESSION DES STAGIAIRES =====
CREATE TABLE IF NOT EXISTS formations_progression_stagiaires (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stagiaire_id INT NOT NULL,
    competence_id INT NOT NULL,
    fonction_id INT NOT NULL,
    pourcentage_acquis DECIMAL(5,2) DEFAULT 0.00,
    heures_effectuees INT DEFAULT 0,
    heures_requises INT DEFAULT 0,
    date_debut DATE,
    date_fin_prevue DATE,
    date_fin_reelle DATE,
    statut ENUM('non_commence', 'en_cours', 'acquis', 'en_retard') DEFAULT 'non_commence',
    derniere_mise_a_jour TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (stagiaire_id) REFERENCES formations_stagiaires(id) ON DELETE CASCADE,
    FOREIGN KEY (competence_id) REFERENCES formations_competences(id) ON DELETE CASCADE,
    FOREIGN KEY (fonction_id) REFERENCES formations_fonctions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_stagiaire_competence (stagiaire_id, competence_id)
);

-- ===== 14. TABLE DES PLANNINGS HEBDOMADAIRES =====
CREATE TABLE IF NOT EXISTS formations_planning_hebdomadaire (
    id INT AUTO_INCREMENT PRIMARY KEY,
    semaine_debut DATE NOT NULL,
    semaine_fin DATE NOT NULL,
    formation_id INT NOT NULL,
    specialite_id INT,
    statut ENUM('brouillon', 'valide', 'archive') DEFAULT 'brouillon',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (formation_id) REFERENCES formations_initiales(id) ON DELETE CASCADE,
    FOREIGN KEY (specialite_id) REFERENCES formations_specialites(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES formations_formateurs(id) ON DELETE SET NULL
);

-- ===== 15. TABLE DES CRÉNEAUX HORAIRES =====
CREATE TABLE IF NOT EXISTS formations_creneaux_horaires (
    id INT AUTO_INCREMENT PRIMARY KEY,
    planning_id INT NOT NULL,
    jour_semaine ENUM('lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi', 'dimanche') NOT NULL,
    heure_debut TIME NOT NULL,
    heure_fin TIME NOT NULL,
    session_id INT,
    type_creneau ENUM('cours', 'pause', 'evaluation', 'libre') DEFAULT 'cours',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (planning_id) REFERENCES formations_planning_hebdomadaire(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES formations_sessions_cours(id) ON DELETE SET NULL
);

-- ===== 16. TABLE DES PARAMÈTRES SYSTÈME =====
CREATE TABLE IF NOT EXISTS formations_parametres (
    id INT AUTO_INCREMENT PRIMARY KEY,
    formation_id INT,
    cle_parametre VARCHAR(100) NOT NULL,
    valeur TEXT,
    description TEXT,
    type_valeur ENUM('string', 'integer', 'decimal', 'boolean', 'json') DEFAULT 'string',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (formation_id) REFERENCES formations_initiales(id) ON DELETE CASCADE,
    UNIQUE KEY unique_formation_parametre (formation_id, cle_parametre)
);

-- ===== INDEX POUR OPTIMISATION DES PERFORMANCES =====

-- Index pour les recherches fréquentes
CREATE INDEX idx_formations_statut ON formations_initiales(statut);
CREATE INDEX idx_specialites_formation ON formations_specialites(formation_id);
CREATE INDEX idx_fonctions_specialite ON formations_fonctions(specialite_id);
CREATE INDEX idx_competences_fonction ON formations_competences(fonction_id);
CREATE INDEX idx_stagiaires_statut ON formations_stagiaires(statut);
CREATE INDEX idx_stagiaires_formation ON formations_stagiaires(formation_id);
CREATE INDEX idx_sessions_date ON formations_sessions_cours(date_debut, date_fin);
CREATE INDEX idx_evaluations_stagiaire ON formations_evaluations(stagiaire_id);
CREATE INDEX idx_evaluations_competence ON formations_evaluations(competence_id);
CREATE INDEX idx_progression_stagiaire ON formations_progression_stagiaires(stagiaire_id);
CREATE INDEX idx_planning_semaine ON formations_planning_hebdomadaire(semaine_debut, semaine_fin);

-- ===== DONNÉES D'EXEMPLE =====

-- Insertion des formations initiales
INSERT INTO formations_initiales (code, nom, description, duree_formation_semaines) VALUES
('SPU', 'Stage Pratique Unité', 'Formation pratique pour les unités d\'artillerie', 16),
('ALPHA', 'Formation Alpha', 'Formation avancée en systèmes d\'armes', 20),
('BETA', 'Formation Beta', 'Formation spécialisée en maintenance', 14);

-- Insertion des spécialités pour SPU
INSERT INTO formations_specialites (formation_id, code, nom, description, duree_specialite_semaines) VALUES
(1, 'GNSS', 'Systèmes de Navigation par Satellite', 'Formation aux systèmes GPS et navigation satellite', 8),
(1, 'DRONE', 'Pilotage et Maintenance de Drones', 'Formation complète aux drones militaires', 6),
(1, 'SOUTIEN', 'Soutien Spécialisé', 'Formation aux techniques de soutien artillerie', 7);

-- Insertion des fonctions pour chaque spécialité
INSERT INTO formations_fonctions (specialite_id, code, nom, description, duree_fonction_heures, niveau_requis) VALUES
-- Fonctions GNSS
(1, 'F1', 'Opérateur GPS', 'Utilisation des équipements GPS de base', 40, 'debutant'),
(1, 'F2', 'Technicien Navigation', 'Maintenance et calibrage des systèmes de navigation', 60, 'intermediaire'),
-- Fonctions DRONE
(2, 'F1', 'Pilote Drone', 'Pilotage des drones de reconnaissance', 80, 'avance'),
(2, 'F2', 'Technicien Drone', 'Maintenance et réparation des drones', 50, 'intermediaire'),
-- Fonctions SOUTIEN
(3, 'F1', 'Coordinateur Logistique', 'Coordination des opérations de soutien', 45, 'intermediaire'),
(3, 'F2', 'Gestionnaire Matériel', 'Gestion des stocks et équipements', 35, 'debutant');

-- Insertion des compétences pour chaque fonction
INSERT INTO formations_competences (fonction_id, code, nom, description, duree_acquisition_heures, niveau_requis, coefficient) VALUES
-- Compétences F1 GNSS (Opérateur GPS)
(1, 'C1', 'Navigation de base', 'Utilisation des fonctions GPS de base', 15, 'debutant', 1.0),
(1, 'C2', 'Lecture de cartes', 'Interprétation des cartes topographiques', 12, 'debutant', 0.8),
(1, 'C3', 'Coordonnées géographiques', 'Maîtrise des systèmes de coordonnées', 13, 'intermediaire', 1.2),
-- Compétences F2 GNSS (Technicien Navigation)
(2, 'C1', 'Calibrage GPS', 'Calibrage et maintenance des équipements', 20, 'intermediaire', 1.5),
(2, 'C2', 'Diagnostic pannes', 'Identification et résolution des pannes', 25, 'avance', 1.8),
(2, 'C3', 'Configuration avancée', 'Paramétrage avancé des systèmes', 15, 'avance', 1.6),
-- Compétences F1 DRONE (Pilote Drone)
(3, 'C1', 'Pilotage manuel', 'Pilotage en mode manuel', 30, 'intermediaire', 2.0),
(3, 'C2', 'Pilotage automatique', 'Utilisation des modes automatiques', 25, 'avance', 1.8),
(3, 'C3', 'Navigation aérienne', 'Navigation et planification de vol', 25, 'avance', 1.7),
-- Compétences F2 DRONE (Technicien Drone)
(4, 'C1', 'Maintenance préventive', 'Entretien régulier des drones', 20, 'intermediaire', 1.3),
(4, 'C2', 'Réparation électronique', 'Réparation des composants électroniques', 30, 'avance', 2.0);

-- Insertion des modules pour chaque spécialité
INSERT INTO formations_modules (nom, description, specialite_id, duree_totale_heures, ordre_sequence) VALUES
-- Modules pour spécialité GNSS
('Module GPS Fondamentaux', 'Formation aux bases du GPS', 1, 40, 1),
('Module Navigation Avancée', 'Techniques de navigation avancées', 1, 35, 2),
('Module Maintenance GPS', 'Maintenance des équipements GPS', 1, 25, 3),
-- Modules pour spécialité DRONE
('Module Théorie Drone', 'Principes de fonctionnement des drones', 2, 30, 1),
('Module Pilotage Drone', 'Apprentissage du pilotage', 2, 50, 2),
('Module Maintenance Drone', 'Maintenance des drones', 2, 40, 3),
-- Modules pour spécialité SOUTIEN
('Module Logistique', 'Techniques logistiques', 3, 35, 1),
('Module Gestion Matériel', 'Gestion des stocks et équipements', 3, 30, 2),
('Module Coordination', 'Coordination des opérations', 3, 25, 3);

-- Insertion des cours
INSERT INTO formations_cours (titre, description, module_id, volume_horaire, type_cours, lieu) VALUES
-- Cours Module GPS Fondamentaux
('Introduction GPS', 'Bases des systèmes GPS', 1, 12, 'theorique', 'Salle A'),
('Pratique GPS Base', 'Utilisation pratique GPS', 1, 15, 'pratique', 'Terrain'),
('Évaluation GPS Base', 'Test des connaissances de base', 1, 3, 'evaluation', 'Salle A'),
-- Cours Module Navigation Avancée
('Navigation Complexe', 'Techniques de navigation avancées', 2, 15, 'theorique', 'Salle B'),
('Cartographie Numérique', 'Utilisation des cartes numériques', 2, 12, 'pratique', 'Salle Info'),
('Projet Navigation', 'Projet pratique de navigation', 2, 8, 'pratique', 'Terrain'),
-- Cours Module Maintenance GPS
('Diagnostic GPS', 'Diagnostic des pannes GPS', 3, 12, 'pratique', 'Atelier'),
('Réparation GPS', 'Techniques de réparation', 3, 13, 'pratique', 'Atelier'),
-- Cours Module Théorie Drone
('Principes Vol', 'Principes de vol des drones', 4, 15, 'theorique', 'Salle C'),
('Réglementation', 'Réglementation aérienne', 4, 10, 'theorique', 'Salle C'),
('Météorologie', 'Impact météo sur le vol', 4, 5, 'theorique', 'Salle C'),
-- Cours Module Pilotage Drone
('Pilotage Manuel', 'Apprentissage pilotage manuel', 5, 20, 'pratique', 'Terrain'),
('Pilotage Automatique', 'Modes automatiques', 5, 15, 'pratique', 'Terrain'),
('Missions Complexes', 'Pilotage en mission', 5, 15, 'pratique', 'Terrain'),
-- Cours Module Maintenance Drone
('Maintenance Préventive', 'Entretien préventif drones', 6, 15, 'pratique', 'Atelier'),
('Réparation Drone', 'Réparation des drones', 6, 15, 'pratique', 'Atelier'),
('Diagnostic Électronique', 'Diagnostic des pannes', 6, 10, 'pratique', 'Atelier');

-- Liaison cours-compétences
INSERT INTO formations_cours_competences (cours_id, competence_id, pourcentage_contribution) VALUES
-- Module GPS Fondamentaux → Compétences F1 GNSS (Opérateur GPS)
(1, 1, 70.00), (1, 2, 30.00),  -- Introduction GPS → C1 Navigation base + C2 Lecture cartes
(2, 1, 30.00), (2, 3, 70.00),  -- Pratique GPS Base → C1 Navigation base + C3 Coordonnées
(3, 1, 50.00), (3, 2, 30.00), (3, 3, 20.00),  -- Évaluation → toutes compétences F1
-- Module Navigation Avancée → Compétences F1 + F2 GNSS
(4, 3, 60.00), (4, 6, 40.00),  -- Navigation Complexe → C3 Coordonnées + C3 Config avancée F2
(5, 2, 80.00), (5, 3, 20.00),  -- Cartographie → C2 Lecture cartes + C3 Coordonnées
(6, 1, 30.00), (6, 2, 30.00), (6, 3, 40.00),  -- Projet → mix compétences
-- Module Maintenance GPS → Compétences F2 GNSS (Technicien Navigation)
(7, 4, 60.00), (7, 5, 40.00),  -- Diagnostic → C1 Calibrage + C2 Diagnostic pannes
(8, 4, 40.00), (8, 5, 60.00),  -- Réparation → C1 Calibrage + C2 Diagnostic pannes
-- Module Théorie Drone → Compétences F1 DRONE (Pilote)
(9, 7, 40.00), (9, 9, 60.00),   -- Principes Vol → C1 Pilotage manuel + C3 Navigation aérienne
(10, 9, 100.00),                -- Réglementation → C3 Navigation aérienne
(11, 9, 100.00),                -- Météorologie → C3 Navigation aérienne
-- Module Pilotage Drone → Compétences F1 DRONE
(12, 7, 100.00),                -- Pilotage Manuel → C1 Pilotage manuel
(13, 8, 100.00),                -- Pilotage Auto → C2 Pilotage automatique
(14, 7, 30.00), (14, 8, 30.00), (14, 9, 40.00),  -- Missions → mix compétences pilotage
-- Module Maintenance Drone → Compétences F2 DRONE (Technicien)
(15, 10, 100.00),               -- Maintenance Préventive → C1 Maintenance préventive
(16, 11, 100.00),               -- Réparation → C2 Réparation électronique
(17, 10, 40.00), (17, 11, 60.00); -- Diagnostic → mix maintenance

-- Insertion des formateurs
INSERT INTO formations_formateurs (matricule, nom, prenom, email, grade, formations_autorisees, specialites_autorisees) VALUES
('F001', 'HASSAN', 'Mohamed', '<EMAIL>', 'Adjudant-Chef', '["SPU", "ALPHA"]', '["GNSS", "DRONE"]'),
('F002', 'ALAMI', 'Fatima', '<EMAIL>', 'Sergent-Chef', '["SPU"]', '["GNSS"]'),
('F003', 'BENALI', 'Ahmed', '<EMAIL>', 'Adjudant', '["SPU", "BETA"]', '["DRONE"]'),
('F004', 'TAZI', 'Youssef', '<EMAIL>', 'Sergent', '["SPU"]', '["SOUTIEN"]');

-- Insertion des stagiaires d'exemple
INSERT INTO formations_stagiaires (matricule, nom, prenom, email, formation_id, specialite_id, date_entree, date_sortie_prevue, statut) VALUES
('S001', 'BENALI', 'Ahmed', '<EMAIL>', 1, 1, '2025-01-15', '2025-05-15', 'en_cours'),
('S002', 'ALAOUI', 'Fatima', '<EMAIL>', 1, 2, '2025-01-15', '2025-04-15', 'en_cours'),
('S003', 'IDRISSI', 'Omar', '<EMAIL>', 1, 1, '2025-02-01', '2025-06-01', 'en_cours'),
('S004', 'CHAKIR', 'Aicha', '<EMAIL>', 1, 3, '2025-01-20', '2025-05-20', 'en_cours'),
('S005', 'MANSOURI', 'Karim', '<EMAIL>', 2, NULL, '2025-02-15', '2025-07-15', 'en_cours');

-- Insertion des paramètres système pour SPU
INSERT INTO formations_parametres (formation_id, cle_parametre, valeur, description, type_valeur) VALUES
(1, 'heures_travail_jour', '8', 'Nombre d\'heures de travail par jour', 'integer'),
(1, 'jours_travail_semaine', '5', 'Nombre de jours de travail par semaine', 'integer'),
(1, 'note_passage_minimum', '10', 'Note minimum pour valider une compétence', 'decimal'),
(1, 'capacite_classe_defaut', '20', 'Capacité par défaut d\'une classe', 'integer'),
(1, 'duree_pause_dejeuner', '60', 'Durée de la pause déjeuner en minutes', 'integer'),
(1, 'heure_debut_cours', '08:00', 'Heure de début des cours', 'string'),
(1, 'heure_fin_cours', '17:00', 'Heure de fin des cours', 'string'),
-- Paramètres globaux (formation_id = NULL pour paramètres système généraux)
(NULL, 'formations_actives', '["SPU", "ALPHA", "BETA"]', 'Liste des formations actives', 'json'),
(NULL, 'annee_formation_courante', '2025', 'Année de formation en cours', 'string');
